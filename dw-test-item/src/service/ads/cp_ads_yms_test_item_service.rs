use crate::config::DwTestItemConfig;
use crate::service::ads::AdsYmsTestItemService;
use chrono::{DateTime, Utc};
use ck_provider::CkConfig;
use common::ads::sink::test_item_bin_handler::TestItemBinHandler;
use common::ads::sink::test_item_program_handler::TestItemProgramHandler;
use common::ads::sink::test_item_site_bin_handler::TestItemSiteBinHandler;
use common::ads::sink::test_item_site_handler::TestItemSiteHandler;
use common::ck::ck_operate::CkOperate;
use common::ck::ck_sink::SinkHandler;
use common::dto::ads::value::die_final_info::DieFinalInfo;
use common::dto::dwd::file_detail::FileDetail;
use common::dto::dwd::sub_test_item_detail::SubTestItemDetail;
use common::dto::ods::product_config::OdsProductConfig;
use common::model::constant::test_area::TestArea;
use common::model::constant::{CP_MAP, INKLESS_MAP, TEST_RAW_DATA};
use mysql_provider::{MySqlProvider, MySqlProviderImpl};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::error::Error;
use std::time::{SystemTime, UNIX_EPOCH};

/// Version flag structure for MySQL query result
/// Corresponds to the VersionFlag case class in Scala
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VersionFlag {
    pub version_flag: bool,
}

/// CP YMS ADS Test Item Service
/// Handles CP (Contact Probe) stage ADS layer test item processing for YMS
///
/// Corresponds to: CpYmsAdsTestItemService.scala
/// case class CpYmsAdsTestItemService(properties: DwTestItemProperties) extends YmsAdsTestItemCommonService(properties)
#[derive(Debug, Clone)]
pub struct CpYmsAdsTestItemService {
    /// Configuration properties for the service
    properties: DwTestItemConfig,
}

impl CpYmsAdsTestItemService {
    /// Create new CpYmsAdsTestItemService
    ///
    /// Corresponds to: CpYmsAdsTestItemService.scala case class constructor
    pub fn new(properties: DwTestItemConfig) -> Self {
        Self { properties }
    }

    /// Calculate CP YMS ADS test item processing
    ///
    /// Corresponds to: CpYmsAdsTestItemService.scala:calculate method
    /// def calculate(spark: SparkSession, fileDetailMap: Broadcast[Map[java.lang.Long, FileDetail]],
    ///              SubTestItemDetail: Dataset[SubTestItemDetail], customer: String, factory: String,
    ///              testArea: String, deviceId: String, lotType: String, testStage: String,
    ///              lotId: String, waferNo: String, dataVersion: String, fileCategory: String): Unit
    pub async fn calculate(
        &self,
        file_detail_map: HashMap<i64, FileDetail>,
        sub_test_item_detail: Vec<Vec<SubTestItemDetail>>,
        customer: &str,
        factory: &str,
        test_area: &str,
        device_id: &str,
        lot_type: &str,
        test_stage: &str,
        lot_id: &str,
        wafer_no: &str,
        _data_version: &str,
        _file_category: &str,
    ) -> Result<(), Box<dyn Error + Send + Sync>> {
        // 获取data_source
        let data_source =
            if TestArea::get_cp_map_data_source_list().contains(&TestArea::of(test_area).unwrap_or(TestArea::CP)) {
                CP_MAP
            } else if TestArea::get_cp_inkless_map_data_source_list()
                .contains(&TestArea::of(test_area).unwrap_or(TestArea::CP))
            {
                INKLESS_MAP
            } else {
                TEST_RAW_DATA
            };

        let onedata_db_name = &self.properties.onedatadbname;

        // Build version SQL query
        let version_sql = self
            .get_version_info_sql()
            .replace("{CUSTOMER}", customer)
            .replace("{FACTORY}", factory)
            .replace("{TEST_AREA}", test_area)
            .replace("{DEVICE_ID}", device_id)
            .replace("{LOT_TYPE}", lot_type)
            .replace("{TEST_STAGE}", test_stage)
            .replace("{LOT_ID}", lot_id)
            .replace("{WAFER_NO}", wafer_no)
            .replace("{ONEDATA_DB_NAME}", onedata_db_name);

        // Create MySQL configuration
        let mysql_config = self.properties.get_mysql_config();
        let mysql_provider = MySqlProviderImpl::new(mysql_config).await?;

        let now = SystemTime::now().duration_since(UNIX_EPOCH)?.as_millis() as i64;

        // Execute version flag query - use a simpler approach
        let version_flag: bool = mysql_provider.count(&version_sql).await?.map(|count| count > 0).unwrap_or(false);

        // Perform tombstone operations for all handlers
        self.execute_tombstone_operations(
            customer,
            factory,
            test_area,
            lot_id,
            lot_type,
            test_stage,
            device_id,
            wafer_no,
            data_source,
            now,
        )
        .await?;

        // Create ClickHouse configuration (placeholder for future use)
        let ck_config = CkConfig {
            url: self.properties.get_ck_address(),
            username: self.properties.ck_username.clone(),
            password: self.properties.ck_password.clone(),
            database: self.properties.ods_db_name.clone(),
            ..Default::default()
        };

        // TODO Get product information - placeholder for now
        let product_infos: Vec<OdsProductConfig> = Vec::new();

        // TODO Get die final information based on version flag - placeholder for now
        let die_final_infos: Vec<DieFinalInfo> = Vec::new();

        // Get current timestamp as version
        let version = SystemTime::now().duration_since(UNIX_EPOCH)?.as_millis() as i64;

        // Create ADS YMS Test Item Service
        AdsYmsTestItemService::new(self.properties.clone())
            .calculate_test_item(
                &sub_test_item_detail,
                &file_detail_map,
                &product_infos,
                &die_final_infos,
                version,
                version_flag,
            )
            .await?;

        Ok(())
    }

    /// Get product configuration SQL query
    ///
    /// Corresponds to: GET_PRODUCT_SQL in CpYmsAdsTestItemService.scala
    fn get_product_sql(&self) -> String {
        r#"
        SELECT DISTINCT DATA_SOURCE
               ,CUSTOMER
               ,SUB_CUSTOMER
               ,FACTORY
               ,FACTORY_SITE
               ,TEST_AREA
               ,TEST_STAGE
               ,DEVICE_ID
               ,PRODUCT
               ,PRODUCT_TYPE
               ,PRODUCT_FAMILY
        FROM {ODS_DB_NAME}.ods_yms_wafermap_config_snapshot_cluster
        WHERE DATA_SOURCE = '{DATA_SOURCE}'
          AND CUSTOMER = '{CUSTOMER}'
          AND FACTORY = '{FACTORY}'
          AND TEST_AREA = '{TEST_AREA}'
          AND TEST_STAGE = '{TEST_STAGE}'
          AND DEVICE_ID = '{DEVICE_ID}'
          AND DT = (SELECT LATEST_PARTITION_VALUE
                    FROM {META_DB_NAME}.meta_table_latest_partition_cluster
                    WHERE TABLE_NAME = 'ods_yms_wafermap_config_snapshot_cluster'
                    AND DATABASE_NAME = '{ODS_DB_NAME}')
        "#
        .to_string()
    }

    /// Get die final information SQL query
    ///
    /// Corresponds to: GET_DIE_FINAL_INFO_SQL in CpYmsAdsTestItemService.scala
    fn get_die_final_info_sql(&self) -> String {
        r#"
        SELECT DISTINCT
         CUSTOMER
        ,FACTORY
        ,TEST_AREA
        ,DEVICE_ID
        ,LOT_TYPE
        ,TEST_STAGE
        ,LOT_ID
        ,WAFER_NO
        ,ECID
        ,C_PART_ID
        ,IS_FINAL_TEST_IGNORE_TP
        FROM {DWD_DB_NAME}.dwd_die_detail_cluster
        WHERE IS_DELETE = 0
          AND TEST_AREA IN ('CP', 'CP(Map)', 'CP(InklessMap)', 'BUMP', 'BUMP(Map)', 'BURNIN', 'BURNIN(Map)', 'WLT', 'WLT(Map)', 'REL(Wafer)', 'REL(Map)', 'ASSY(Wafer)', 'ASSY(Map)')
          AND CUSTOMER = '{CUSTOMER}'
          AND FACTORY = '{FACTORY}'
          AND TEST_AREA = '{TEST_AREA}'
          AND DEVICE_ID = '{DEVICE_ID}'
          AND LOT_TYPE = '{LOT_TYPE}'
          AND TEST_STAGE = '{TEST_STAGE}'
          AND LOT_ID = '{LOT_ID}'
          AND WAFER_NO = '{WAFER_NO}'
        "#.to_string()
    }

    /// Get version information SQL query
    ///
    /// Corresponds to: GET_VERSION_INFO_SQL in CpYmsAdsTestItemService.scala
    fn get_version_info_sql(&self) -> String {
        r#"
        SELECT COUNT(*) as count
        FROM {ONEDATA_DB_NAME}.dw_layer_calculate_pool
        WHERE process_status = 'SUCCESS'
        AND dw_layer = 'DWD'
        AND customer = '{CUSTOMER}'
        AND factory = '{FACTORY}'
        AND test_area = '{TEST_AREA}'
        AND device_id = '{DEVICE_ID}'
        AND lot_type = '{LOT_TYPE}'
        AND test_stage = '{TEST_STAGE}'
        AND lot_id = '{LOT_ID}'
        AND wafer_no = '{WAFER_NO}'
        AND version NOT IN ('1.11.0','1.12.0','1.12.1','1.12.2','1.12.3','1.13.0','2.0.0','2.1.0','2.1.1','2.2.0','2.3.0','2.4.0','2.4.1','2.5.0','2.6.0','2.7.0','2.7.1','2.8.0','2.9.0','2.9.1','2.9.2')
        "#.to_string()
    }

    /// Execute tombstone operations for multiple handlers
    ///
    /// This method reduces code duplication by handling the common tombstone operation logic
    async fn execute_tombstone_operations(
        &self,
        customer: &str,
        factory: &str,
        test_area: &str,
        lot_id: &str,
        lot_type: &str,
        test_stage: &str,
        device_id: &str,
        wafer_no: &str,
        data_source: &str,
        timestamp: i64,
    ) -> Result<(), Box<dyn Error + Send + Sync>> {
        let timestamp_dt = DateTime::from_timestamp_millis(timestamp).unwrap_or_else(Utc::now);

        // Create handlers and execute tombstone operations
        let site_bin_handler = TestItemSiteBinHandler::new(self.properties.ads_db_name.clone());
        let bin_handler = TestItemBinHandler::new(self.properties.ads_db_name.clone());

        self.execute_single_tombstone_operation(
            vec![&site_bin_handler, &bin_handler],
            customer,
            factory,
            test_area,
            lot_id,
            lot_type,
            test_stage,
            device_id,
            wafer_no,
            data_source,
            timestamp_dt,
        )
        .await?;

        self.execute_single_tombstone_operation(
            &bin_handler,
            customer,
            factory,
            test_area,
            lot_id,
            lot_type,
            test_stage,
            device_id,
            wafer_no,
            data_source,
            timestamp_dt,
        )
        .await?;

        let site_handler = TestItemSiteHandler::new(self.properties.ads_db_name.clone());
        self.execute_single_tombstone_operation(
            &site_handler,
            customer,
            factory,
            test_area,
            lot_id,
            lot_type,
            test_stage,
            device_id,
            wafer_no,
            data_source,
            timestamp_dt,
        )
        .await?;

        let program_handler = TestItemProgramHandler::new(self.properties.ads_db_name.clone());
        self.execute_single_tombstone_operation(
            &program_handler,
            customer,
            factory,
            test_area,
            lot_id,
            lot_type,
            test_stage,
            device_id,
            wafer_no,
            data_source,
            timestamp_dt,
        )
        .await?;

        Ok(())
    }

    /// Execute tombstone operation for a single handler
    async fn execute_single_tombstone_operation<T>(
        &self,
        handlers: &<dyn SinkHandler<T>>,
        customer: &str,
        factory: &str,
        test_area: &str,
        lot_id: &str,
        lot_type: &str,
        test_stage: &str,
        device_id: &str,
        wafer_no: &str,
        data_source: &str,
        timestamp_dt: DateTime<Utc>,
    ) -> Result<(), Box<dyn Error + Send + Sync>>
    where
        T: clickhouse::Row,
    {
        for handler in handlers {
            let table_full_name = format!("{}.{}", handler.db_name(), handler.table_name());
            let partition_expr = handler.partition_expr().replace("{DATA_SOURCE}", data_source);

            CkOperate::tombstone_ck(
                customer,
                factory,
                test_area,
                lot_id,
                lot_type,
                test_stage,
                device_id,
                None, // lot_bucket
                wafer_no,
                &table_full_name,
                &self.properties.get_ck_address(),
                &self.properties.get_ck_address_list(),
                &self.properties.ck_username,
                &self.properties.ck_password,
                &partition_expr,
                Some(timestamp_dt),
            )
            .await
            .map_err(|e| -> Box<dyn Error + Send + Sync> {
                Box::new(std::io::Error::new(
                    std::io::ErrorKind::Other,
                    format!("Tombstone operation failed for table {}: {}", table_full_name, e),
                ))
            })?;
        }
        Ok(())
    }
}
